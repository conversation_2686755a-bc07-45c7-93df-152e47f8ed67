"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TokenCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCacheService = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const redis_1 = require("redis");
const config_1 = require("@nestjs/config");
let TokenCacheService = TokenCacheService_1 = class TokenCacheService {
    cacheManager;
    configService;
    logger = new common_1.Logger(TokenCacheService_1.name);
    redisClient;
    constructor(cacheManager, configService) {
        this.cacheManager = cacheManager;
        this.configService = configService;
    }
    async onModuleInit() {
        this.redisClient = (0, redis_1.createClient)({
            url: `redis://${this.configService.get('REDIS_HOST', 'localhost')}:${this.configService.get('REDIS_PORT', 6379)}`,
        });
        this.redisClient.on('error', (error) => {
            this.logger.error('Redis Client Error:', error);
        });
        this.redisClient.on('connect', () => {
            this.logger.log('Connected to Redis');
        });
        await this.redisClient.connect();
    }
    async storeRefreshToken(userId, refreshToken, ttl = 604800) {
        const key = `refresh_token:${refreshToken}`;
        this.logger.debug(`Attempting to store refresh token in Redis...`);
        this.logger.debug(`Key: ${key}`);
        this.logger.debug(`UserId: ${userId}`);
        this.logger.debug(`TTL: ${ttl} seconds`);
        try {
            await this.redisClient.set(key, userId, {
                EX: ttl
            });
            const stored = await this.redisClient.get(key);
            this.logger.debug(`Cache verification - Key exists: ${stored !== null}`);
            this.logger.debug(`Cache verification - Stored value: ${stored}`);
            if (stored === null) {
                throw new Error('Token was not stored in Redis');
            }
        }
        catch (error) {
            this.logger.error(`Failed to store refresh token: ${error.message}`);
            throw error;
        }
    }
    async getUserIdByRefreshToken(refreshToken) {
        const key = `refresh_token:${refreshToken}`;
        try {
            const result = await this.redisClient.get(key);
            this.logger.debug(`Retrieved value for ${key}: ${result}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to get refresh token: ${error.message}`);
            throw error;
        }
    }
    async removeRefreshToken(refreshToken) {
        const key = `refresh_token:${refreshToken}`;
        try {
            await this.redisClient.del(key);
            this.logger.debug(`Removed refresh token: ${key}`);
        }
        catch (error) {
            this.logger.error(`Failed to remove refresh token: ${error.message}`);
            throw error;
        }
    }
    async removeAllUserRefreshTokens(userId) {
        this.logger.warn('removeAllUserRefreshTokens not implemented yet');
    }
};
exports.TokenCacheService = TokenCacheService;
exports.TokenCacheService = TokenCacheService = TokenCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object, config_1.ConfigService])
], TokenCacheService);
//# sourceMappingURL=token-cache.service.js.map