import { OnModuleInit } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
export declare class TokenCacheService implements OnModuleInit {
    private cacheManager;
    private configService;
    private readonly logger;
    private redisClient;
    constructor(cacheManager: Cache, configService: ConfigService);
    onModuleInit(): Promise<void>;
    storeRefreshToken(userId: string, refreshToken: string, ttl?: number): Promise<void>;
    getUserIdByRefreshToken(refreshToken: string): Promise<string | null>;
    removeRefreshToken(refreshToken: string): Promise<void>;
    removeAllUserRefreshTokens(userId: string): Promise<void>;
}
