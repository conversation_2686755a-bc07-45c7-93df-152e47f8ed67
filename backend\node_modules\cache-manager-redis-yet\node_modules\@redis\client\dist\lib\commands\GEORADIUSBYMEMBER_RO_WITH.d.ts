import { RedisCommandArgument, RedisCommandArguments } from '.';
import { GeoReplyWith, GeoSearchOptions, GeoUnits } from './generic-transformers';
export { FIRST_KEY_INDEX, IS_READ_ONLY } from './GEORADIUSBYMEMBER_RO';
export declare function transformArguments(key: RedisCommandArgument, member: string, radius: number, unit: GeoUnits, replyWith: Array<GeoReplyWith>, options?: GeoSearchOptions): RedisCommandArguments;
export { transformGeoMembersWithReply as transformReply } from './generic-transformers';
